import React, { useState, useEffect, useRef } from "react";
import { FaFlag } from "react-icons/fa";
import { FaUserGroup } from "react-icons/fa6";
import { HiDotsHorizontal } from "react-icons/hi";
import { HiUserCircle } from "react-icons/hi";
import NoticeActionMenu from "../components/NoticeActionMenu";
import { PinIcon } from "../components/PinPost";
import UserCountDisplay from "../components/UserCountDisplay";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

// Priority color mapping - consistent with PriorityDropdown component
const PRIORITY_COLORS = {
  urgent: "text-red-500",
  high: "text-yellow-500",
  normal: "text-primary",
  low: "text-gray-400"
};

const getPriorityColor = (priority) => {
  return PRIORITY_COLORS[priority?.toLowerCase()] || "text-gray-500";
};

/**
 * NoticeListPreview Component
 * Renders the list of notices with their preview cards in a row-wise masonry layout
 */
const NoticeListPreview = ({
  // Data props
  notices,
  loading,

  // UI state props
  openDropdownId,
  dropdownRef,
  currentTab,

  // Handlers
  handleDropdownToggle,
  onEdit,
  onDelete,
  onExpire,
  onRestore,
  onPinToggle,
  onImageClick,
  onViewHistory,
  activeTab,

  // Utility functions
  isDocument,
  getFileIcon
}) => {
  const containerRef = useRef(null);
  const [positions, setPositions] = useState([]);
  const [containerHeight, setContainerHeight] = useState(0);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  // Two-pass layout: first render naturally, then measure and position
  useEffect(() => {
    if (!containerRef.current || notices.length === 0) {
      setPositions([]);
      setContainerHeight(0);
      setIsLayoutReady(false);
      return;
    }

    const calculateLayout = () => {
      const containerWidth = containerRef.current.offsetWidth;
      const cardWidth = 350; // Fixed card width
      const gap = 8; // 8px gap between cards

      // Calculate number of columns that fit
      const columns = Math.max(
        1,
        Math.floor((containerWidth + gap) / (cardWidth + gap))
      );
      const columnHeights = new Array(columns).fill(0);
      const newPositions = [];

      // Get all card elements to measure their actual heights
      const cardElements =
        containerRef.current.querySelectorAll("[data-card-id]");

      if (cardElements.length === notices.length) {
        // All cards are rendered, measure their heights
        notices.forEach((_, index) => {
          const cardElement = cardElements[index];
          const cardHeight = cardElement ? cardElement.offsetHeight : 300;

          // Find the shortest column for optimal space usage
          const shortestColumn = columnHeights.indexOf(
            Math.min(...columnHeights)
          );

          // Position the card
          newPositions.push({
            left: shortestColumn * (cardWidth + gap),
            top: columnHeights[shortestColumn]
          });

          // Update column height with actual card height + gap
          columnHeights[shortestColumn] += cardHeight + gap;
        });

        setPositions(newPositions);
        setContainerHeight(Math.max(...columnHeights) - gap);
        setIsLayoutReady(true);
      } else {
        // Cards not fully rendered yet, wait for next frame
        setIsLayoutReady(false);
        requestAnimationFrame(calculateLayout);
      }
    };

    // Start layout calculation
    setIsLayoutReady(false);
    const timeoutId = setTimeout(calculateLayout, 50);

    return () => clearTimeout(timeoutId);
  }, [notices]);

  // Recalculate layout on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsLayoutReady(false);
      setTimeout(() => {
        if (containerRef.current && notices.length > 0) {
          const calculateLayout = () => {
            const containerWidth = containerRef.current.offsetWidth;
            const cardWidth = 350;
            const gap = 8;

            const columns = Math.max(
              1,
              Math.floor((containerWidth + gap) / (cardWidth + gap))
            );
            const columnHeights = new Array(columns).fill(0);
            const newPositions = [];

            const cardElements =
              containerRef.current.querySelectorAll("[data-card-id]");

            if (cardElements.length === notices.length) {
              notices.forEach((_, index) => {
                const cardElement = cardElements[index];
                const cardHeight = cardElement ? cardElement.offsetHeight : 300;

                const shortestColumn = columnHeights.indexOf(
                  Math.min(...columnHeights)
                );

                newPositions.push({
                  left: shortestColumn * (cardWidth + gap),
                  top: columnHeights[shortestColumn]
                });

                columnHeights[shortestColumn] += cardHeight + gap;
              });

              setPositions(newPositions);
              setContainerHeight(Math.max(...columnHeights) - gap);
              setIsLayoutReady(true);
            }
          };

          calculateLayout();
        }
      }, 100);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [notices]);
  const formatDateTime = (dateString, timeString) => {
    if (!dateString) return "";

    try {
      const date = new Date(dateString);
      const formattedDate = date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });

      if (timeString) {
        // Parse time string (assuming format like "14:30:00" or "14:30")
        const timeParts = timeString.split(":");
        const hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10);

        // Convert to 12-hour format
        const period = hours >= 12 ? "pm" : "am";
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        const formattedTime = `${displayHours}:${minutes
          .toString()
          .padStart(2, "0")}${period}`;

        return `${formattedDate} at ${formattedTime}`;
      }

      return formattedDate;
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  const isImage = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension);
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!notices || notices.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[300px] text-center w-full">
        <h3 className="text-xl font-semibold text-gray-900">
          No notices found
        </h3>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="relative w-full"
      style={{
        height: isLayoutReady ? `${containerHeight}px` : "auto",
        minHeight: isLayoutReady ? `${containerHeight}px` : "300px"
      }}
    >
      {notices.map((notice, index) => {
        const position = positions[index] || { left: 0, top: 0 };

        return (
          <div
            key={notice.id}
            data-card-id={notice.id}
            className={`w-full rounded-lg bg-white border border-gray-200 shadow-sm p-4 flex flex-col gap-3 ${
              isLayoutReady ? "absolute" : "relative mb-4"
            }`}
            style={
              isLayoutReady
                ? {
                    left: `${position.left}px`,
                    top: `${position.top}px`,
                    width: "350px",
                    transition: "all 0.3s ease"
                  }
                : { width: "350px", maxWidth: "100%" }
            }
          >
            {/* Header with Pin and Menu */}
            <div className="flex justify-between items-start">
              <div className="flex items-center gap-2">
                <PinIcon
                  isPinned={notice.pinned || notice.isPinned}
                  onClick={() => onPinToggle && onPinToggle(notice)}
                  className="cursor-pointer"
                />
                <FaFlag className={`w-4 h-4 ${getPriorityColor(notice.priority)}`} />
              </div>

              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => handleDropdownToggle && handleDropdownToggle(notice.id)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <HiDotsHorizontal className="w-5 h-5 text-gray-500" />
                </button>

                {openDropdownId === notice.id && (
                  <NoticeActionMenu
                    notice={notice}
                    onEdit={() => onEdit && onEdit(notice.id)}
                    onDelete={() => onDelete && onDelete(notice)}
                    onExpire={() => onExpire && onExpire(notice)}
                    onRestore={() => onRestore && onRestore(notice)}
                    onViewHistory={() => onViewHistory && onViewHistory(notice)}
                    activeTab={activeTab}
                  />
                )}
              </div>
            </div>

            {/* Title */}
            <div className="flex-1">
              <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2">
                {notice.internalTitle || notice.title || "Notice"}
              </h3>
            </div>

            {/* Attachments */}
            {notice.attachments && notice.attachments.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {notice.attachments.map((attachment, index) => {
                  const fileName = attachment.file_name || attachment.name || `attachment-${index}`;
                  const fileUrl = attachment.file_url || attachment.url || attachment;

                  if (isImage(fileName)) {
                    return (
                      <div
                        key={index}
                        className="relative cursor-pointer group"
                        onClick={() => onImageClick && onImageClick(attachment, notice)}
                      >
                        <img
                          src={fileUrl}
                          alt={fileName}
                          className="w-16 h-16 object-cover rounded border border-gray-200 hover:border-primary transition-colors"
                        />
                      </div>
                    );
                  } else if (isDocument && isDocument(fileName)) {
                    return (
                      <div
                        key={index}
                        className="flex items-center gap-2 p-2 border border-gray-200 rounded cursor-pointer hover:border-primary transition-colors"
                        onClick={() => handleDocumentClick && handleDocumentClick(attachment)}
                      >
                        {getFileIcon && getFileIcon(fileName)}
                        <span className="text-xs text-gray-600 truncate max-w-[100px]">
                          {fileName}
                        </span>
                      </div>
                    );
                  }
                  return null;
                })}
              </div>
            )}

            {/* Label */}
            {notice.label && (
              <div className="flex flex-wrap gap-1">
                {notice.label.split(",").map((label, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {label.trim()}
                  </span>
                ))}
              </div>
            )}

            {/* Date/Time Information */}
            <div className="text-[11px] text-gray-500 flex gap-4">
              <span>
                Start: {formatDateTime(notice.startDate, notice.startTime)}
              </span>
              <span>
                Expire: {formatDateTime(notice.endDate, notice.endTime)}
              </span>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-100">
              <div className="flex items-center gap-2">
                <HiUserCircle className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {notice.author || notice.creatorName || "Unknown"}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <FaUserGroup className="w-4 h-4 text-gray-400" />
                <UserCountDisplay
                  targetUnitsData={notice.target_units_data || []}
                  className="text-sm text-gray-600"
                />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default NoticeListPreview;
